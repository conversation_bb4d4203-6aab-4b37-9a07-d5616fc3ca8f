#!/usr/bin/env python3
"""
检查mendeleev库中是否有common_oxidation_states或相关的氧化态数据
"""

try:
    from mendeleev import element
except ImportError:
    print("错误: 未安装mendeleev库")
    print("请运行: pip install mendeleev")
    exit(1)

def check_oxidation_states():
    """检查Ba元素的氧化态相关数据"""
    
    ba = element('Ba')
    
    print(f"元素: {ba.name} ({ba.symbol})")
    print(f"原子序数: {ba.atomic_number}")
    print("=" * 50)
    
    # 1. 检查是否有common_oxidation_states属性
    print("1. 检查common_oxidation_states属性:")
    print("-" * 30)
    
    if hasattr(ba, 'common_oxidation_states'):
        common_ox = getattr(ba, 'common_oxidation_states')
        print(f"common_oxidation_states: {common_ox}")
    else:
        print("未找到common_oxidation_states属性")
    
    # 2. 检查所有可能的氧化态相关属性
    print("\n2. 检查所有氧化态相关属性:")
    print("-" * 30)
    
    oxidation_attrs = [
        'oxidation_states', 'common_oxidation_states', 'oxidation_state',
        'oxistates', 'ox_states', 'oxidation', 'valence_states'
    ]
    
    found_attrs = []
    for attr in oxidation_attrs:
        if hasattr(ba, attr):
            try:
                value = getattr(ba, attr)
                print(f"{attr}: {value}")
                found_attrs.append(attr)
            except Exception as e:
                print(f"{attr}: 获取失败 - {e}")
    
    if not found_attrs:
        print("未找到明确的氧化态属性")
    
    # 3. 检查所有包含"ox"的属性
    print("\n3. 检查所有包含'ox'的属性:")
    print("-" * 30)
    
    all_attrs = [attr for attr in dir(ba) if not attr.startswith('_')]
    ox_attrs = [attr for attr in all_attrs if 'ox' in attr.lower()]
    
    for attr in ox_attrs:
        try:
            value = getattr(ba, attr)
            print(f"{attr}: {value}")
        except Exception as e:
            print(f"{attr}: 获取失败 - {e}")
    
    # 4. 检查是否有氧化态方法
    print("\n4. 检查氧化态相关方法:")
    print("-" * 30)
    
    oxidation_methods = [
        'oxidation_states', 'get_oxidation_states', 'common_oxidation_states'
    ]
    
    for method in oxidation_methods:
        if hasattr(ba, method):
            try:
                attr_obj = getattr(ba, method)
                if callable(attr_obj):
                    result = attr_obj()
                    print(f"{method}(): {result}")
                else:
                    print(f"{method}: {attr_obj}")
            except Exception as e:
                print(f"{method}: 调用失败 - {e}")
    
    # 5. 检查_oxidation_states属性（可能是私有属性）
    print("\n5. 检查私有氧化态属性:")
    print("-" * 30)
    
    private_attrs = ['_oxidation_states', '_oxidation_state', '_oxistates']
    for attr in private_attrs:
        if hasattr(ba, attr):
            try:
                value = getattr(ba, attr)
                print(f"{attr}: {value}")
                
                # 如果是列表或集合，显示详细信息
                if hasattr(value, '__len__') and not isinstance(value, str):
                    print(f"  类型: {type(value)}")
                    print(f"  长度: {len(value)}")
                    if len(value) > 0:
                        print(f"  内容: {list(value) if hasattr(value, '__iter__') else value}")
                        
                        # 如果是对象列表，显示第一个对象的属性
                        if hasattr(value, '__iter__') and len(value) > 0:
                            first_item = next(iter(value))
                            if hasattr(first_item, '__dict__') or hasattr(first_item, '__slots__'):
                                print(f"  第一个对象的属性: {dir(first_item)}")
                                # 尝试获取氧化态值
                                for ox_attr in ['oxidation_state', 'value', 'state', 'ox']:
                                    if hasattr(first_item, ox_attr):
                                        ox_value = getattr(first_item, ox_attr)
                                        print(f"    {ox_attr}: {ox_value}")
                
            except Exception as e:
                print(f"{attr}: 获取失败 - {e}")

def check_multiple_elements():
    """检查多个元素的氧化态数据以确认模式"""
    
    print("\n" + "=" * 50)
    print("检查多个元素的氧化态数据:")
    print("=" * 50)
    
    elements = ['H', 'C', 'N', 'O', 'Na', 'Mg', 'Al', 'Fe', 'Ba']
    
    for symbol in elements:
        try:
            elem = element(symbol)
            print(f"\n{elem.name} ({symbol}):")
            
            # 检查oxistates
            if hasattr(elem, 'oxistates'):
                oxistates = getattr(elem, 'oxistates')
                print(f"  oxistates: {oxistates}")
            
            # 检查_oxidation_states
            if hasattr(elem, '_oxidation_states'):
                ox_states = getattr(elem, '_oxidation_states')
                if hasattr(ox_states, '__len__'):
                    print(f"  _oxidation_states (长度: {len(ox_states)})")
                    if len(ox_states) > 0:
                        # 提取氧化态值
                        states = []
                        for state in ox_states:
                            if hasattr(state, 'oxidation_state'):
                                states.append(getattr(state, 'oxidation_state'))
                        print(f"  氧化态值: {sorted(states)}")
                else:
                    print(f"  _oxidation_states: {ox_states}")
            
        except Exception as e:
            print(f"{symbol}: 获取失败 - {e}")

def summary_oxidation_states():
    """总结mendeleev库中氧化态数据的获取方法"""

    print("\n" + "=" * 60)
    print("Mendeleev库氧化态数据获取方法总结:")
    print("=" * 60)

    ba = element('Ba')

    print(f"以Ba元素为例:")
    print(f"元素: {ba.name} ({ba.symbol})")
    print()

    # 方法1: oxistates属性 - 常见氧化态
    print("1. 获取常见氧化态 (oxistates):")
    print(f"   ba.oxistates = {ba.oxistates}")
    print("   这相当于'common_oxidation_states'")
    print()

    # 方法2: oxidation_states()方法 - 常见氧化态
    print("2. 获取常见氧化态 (方法调用):")
    print(f"   ba.oxidation_states() = {ba.oxidation_states()}")
    print("   与oxistates属性相同")
    print()

    # 方法3: _oxidation_states属性 - 所有氧化态
    print("3. 获取所有氧化态 (_oxidation_states):")
    all_ox_states = [ox.oxidation_state for ox in ba._oxidation_states]
    main_ox_states = [ox.oxidation_state for ox in ba._oxidation_states if ox.category == 'main']
    extended_ox_states = [ox.oxidation_state for ox in ba._oxidation_states if ox.category == 'extended']

    print(f"   所有氧化态: {sorted(all_ox_states)}")
    print(f"   主要氧化态: {sorted(main_ox_states)}")
    print(f"   扩展氧化态: {sorted(extended_ox_states)}")
    print()

    print("结论:")
    print("- mendeleev库中没有'common_oxidation_states'属性")
    print("- 使用'oxistates'属性获取常见氧化态")
    print("- 使用'oxidation_states()'方法也可获取常见氧化态")
    print("- 使用'_oxidation_states'属性可获取所有氧化态的详细信息")

if __name__ == "__main__":
    check_oxidation_states()
    check_multiple_elements()
    summary_oxidation_states()
