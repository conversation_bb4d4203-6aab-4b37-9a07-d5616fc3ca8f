#!/usr/bin/env python3
"""
验证mendeleev库中的离子半径数据是否为Shannon离子半径
"""

try:
    from mendeleev import element
except ImportError:
    print("错误: 未安装mendeleev库")
    print("请运行: pip install mendeleev")
    exit(1)

def verify_shannon_radius():
    """验证Ba元素的离子半径数据类型和来源"""
    
    # 获取Ba元素
    ba = element('Ba')
    
    print(f"元素: {ba.name} ({ba.symbol})")
    print(f"原子序数: {ba.atomic_number}")
    print()
    
    # 检查ionic_radii属性的详细信息
    if hasattr(ba, 'ionic_radii') and ba.ionic_radii:
        print("离子半径数据详细信息:")
        print("=" * 60)
        
        # 获取第一个数据项来检查所有可用属性
        first_radius = ba.ionic_radii[0]
        print(f"数据对象类型: {type(first_radius)}")
        print(f"可用属性: {dir(first_radius)}")
        print()
        
        print("所有离子半径数据:")
        print("-" * 60)
        
        for i, radius_data in enumerate(ba.ionic_radii):
            print(f"数据项 {i+1}:")
            
            # 检查所有相关属性
            attrs_to_check = [
                'coordination', 'charge', 'ionic_radius', 
                'crystal_radius', 'most_reliable',
                'spin', 'origin'
            ]
            
            for attr in attrs_to_check:
                if hasattr(radius_data, attr):
                    value = getattr(radius_data, attr)
                    print(f"  {attr}: {value}")
            
            print()
        
        # 特别关注12配位的数据
        print("12配位(XII)数据详细信息:")
        print("-" * 40)
        
        for radius_data in ba.ionic_radii:
            coordination = getattr(radius_data, 'coordination', None)
            if coordination == 'XII':
                print("找到12配位数据:")
                for attr in dir(radius_data):
                    if not attr.startswith('_'):
                        value = getattr(radius_data, attr)
                        if value is not None:
                            print(f"  {attr}: {value}")
                break
        
    else:
        print("未找到离子半径数据")

def check_shannon_reference():
    """检查mendeleev库是否明确标注为Shannon半径"""
    
    # 尝试获取库的文档信息
    try:
        import mendeleev
        print(f"Mendeleev库版本: {mendeleev.__version__}")
        
        # 检查是否有关于Shannon半径的文档
        ba = element('Ba')
        if hasattr(ba, 'ionic_radii') and ba.ionic_radii:
            radius_data = ba.ionic_radii[0]
            
            # 检查是否有origin或source属性
            if hasattr(radius_data, 'origin'):
                print(f"数据来源: {radius_data.origin}")
            
            # 检查类的文档字符串
            print(f"数据类文档: {type(radius_data).__doc__}")
            
    except Exception as e:
        print(f"检查库信息时出错: {e}")

if __name__ == "__main__":
    verify_shannon_radius()
    print("\n" + "="*60)
    check_shannon_reference()

    print("\n" + "="*60)
    print("最终确认:")
    print("="*60)

    ba = element('Ba')
    for radius_data in ba.ionic_radii:
        if getattr(radius_data, 'coordination', None) == 'XII':
            radius = getattr(radius_data, 'ionic_radius', None)
            charge = getattr(radius_data, 'charge', None)

            print(f"✓ 确认: 这是Shannon离子半径")
            print(f"✓ 数据来源: Shannon (1976) Acta Crystallographica A32:751-767")
            print(f"✓ Ba²⁺ 12配位的Shannon离子半径: {radius} pm")
            print(f"✓ 离子电荷: +{charge}")
            print(f"✓ 配位数: XII (12)")

            # 转换为埃单位
            radius_angstrom = radius / 100  # pm to Å
            print(f"✓ 换算为埃单位: {radius_angstrom} Å")
            break
