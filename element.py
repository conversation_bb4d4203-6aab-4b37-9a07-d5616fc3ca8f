from mendeleev import element

# 1. 获取钡 (Ba) 元素对象
try:
    ba = element('Ba')
except KeyError:
    print("无法找到元素 'Ba'。请检查元素符号是否正确。")
    exit()

# 2. 定义要查找的目标条件
target_coordination = 'XII'
target_charge = 2
target_method = 'Shannon'

# 3. 初始化一个变量来存储结果
found_radius = None

# 4. 遍历元素的所有离子半径数据
for radius in ba.ionic_radii:
    # 检查是否同时满足所有条件
    if (radius.coordination == target_coordination and
            radius.charge == target_charge and
            target_method in radius.method):
        found_radius = radius
        break  # 找到后即可退出循环

# 5. 打印结果
if found_radius:
    print(f"成功找到钡 (Ba) 的香农离子半径：")
    print("-" * 30)
    print(f"电荷 (Charge): +{found_radius.charge}")
    print(f"配位数 (Coordination): {found_radius.coordination}")
    print(f"半径 (Radius): {found_radius.radius} pm")
    print(f"半径 (Radius in Å): {found_radius.radius_w} Å")
    print(f"数据来源 (Method): {found_radius.method}")
else:
    print(f"未能在数据库中找到电荷为 +{target_charge}、配位数为 {target_coordination} 的钡 (Ba) 的香农离子半径。")