#!/usr/bin/env python3
"""
获取mendeleev库中12配位的Ba的shannon离子半径
"""

try:
    from mendeleev import element
except ImportError:
    print("错误: 未安装mendeleev库")
    print("请运行: pip install mendeleev")
    exit(1)

def get_ba_shannon_radius():
    """获取Ba元素的shannon离子半径信息"""

    # 获取Ba元素
    ba = element('Ba')

    print(f"元素: {ba.name} ({ba.symbol})")
    print(f"原子序数: {ba.atomic_number}")
    print()

    # 获取shannon离子半径数据
    if hasattr(ba, 'ionic_radii') and ba.ionic_radii:
        print("Shannon离子半径数据:")
        print("-" * 50)

        # 查找12配位的数据
        found_12_coord = False

        for radius_data in ba.ionic_radii:
            coordination = getattr(radius_data, 'coordination', None)
            charge = getattr(radius_data, 'charge', None)
            radius = getattr(radius_data, 'ionic_radius', None)

            print(f"配位数: {coordination}, 电荷: {charge}, 半径: {radius} Å")

            # 检查是否为12配位 (XII是罗马数字12)
            if coordination == 'XII':
                found_12_coord = True
                print(f"*** 找到12配位的Ba离子半径: {radius} Å (电荷: {charge}) ***")
                return radius  # 直接返回半径值

        if not found_12_coord:
            print("\n未找到12配位的Ba离子半径数据")
            print("可用的配位数:")
            coords = set()
            for radius_data in ba.ionic_radii:
                coordination = getattr(radius_data, 'coordination', None)
                if coordination:
                    coords.add(coordination)
            print(sorted(coords))
            return None
    else:
        print("未找到shannon离子半径数据")
        return None


def get_ba_12_coord_radius_only():
    """仅获取Ba元素12配位的shannon离子半径值"""
    ba = element('Ba')

    if hasattr(ba, 'ionic_radii') and ba.ionic_radii:
        for radius_data in ba.ionic_radii:
            coordination = getattr(radius_data, 'coordination', None)
            radius = getattr(radius_data, 'ionic_radius', None)

            if coordination == 'XII':
                return radius
    return None

if __name__ == "__main__":
    # 显示详细信息
    radius = get_ba_shannon_radius()

    print("\n" + "="*50)
    print("简洁结果:")

    # 仅获取半径值
    radius_only = get_ba_12_coord_radius_only()
    if radius_only:
        print(f"Ba²⁺ 12配位的Shannon离子半径: {radius_only} Å")
    else:
        print("未找到Ba²⁺ 12配位的Shannon离子半径数据")
